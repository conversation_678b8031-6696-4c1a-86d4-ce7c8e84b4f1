<template>
  <el-tabs v-model="activeName" type="card">
    <el-tab-pane label="基本信息" name="baseInfo">
      <el-form :model="formData" :rules="rules" inline :disabled="isPreview">
        <el-form-item label="名称" prop="Name">
          <el-input v-model="formData.Name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="别名">
          <el-input v-model="formData.AliasName" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="编码" prop="Code">
          <el-input v-model="formData.Code" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="拼音码" prop="PinyinCode">
          <el-input v-model="formData.PinyinCode" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch v-model="formData.Enable" />
        </el-form-item>
        <el-form-item label="类别">
          <el-select v-model="formData.Type" placeholder="类别">
            <el-option
              v-for="item in typeList"
              :key="item.Value"
              :label="item.Name"
              :value="item.Value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="频率" prop="Freq">
          <span>一次</span>
          <el-input-number
            v-model="formData.Freq"
            style="width: 100px; margin: 0 10px"
            :min="0"
            :max="100000"
            :step="1"
          />
          <span>次</span>
        </el-form-item>
        <el-form-item label="运动时间">
          <el-input v-model="formData.ActionTime" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="运动强度">
          <el-input v-model="formData.ActionStrength" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item class="margin-bottom-6" label="厂商" prop="Manufacturer">
          <el-select v-model="formData.Manufacturer" placeholder="请选择厂商" clearable>
            <el-option label="易脑复苏VR" :value="1" />
            <el-option label="赛客呼吸康复" :value="2" />
            <el-option label="脑动极光" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="formData.Manufacturer === 2" label="厂商动作" prop="MFType">
          <el-select v-model="formData.MFType" placeholder="请选择厂商动作" clearable>
            <el-option label="呼气训练" :value="1" />
            <el-option label="吸气训练" :value="2" />
            <el-option label="气道廓清" :value="3" />
          </el-select>
        </el-form-item>
        <template v-if="formData.Type === 0">
          <br />
          <el-form-item prop="GroupCount" label="组数">
            <el-input v-model="formData.GroupCount" type="text" style="width: 80px" />
          </el-form-item>
          <el-form-item prop="EachGroupCount" label="每组次数">
            <el-input v-model="formData.EachGroupCount" type="text" style="width: 80px" />
          </el-form-item>
          <el-form-item class="margin-bottom-6" label="时长" prop="Duration">
            <el-input
              v-model="formData.Duration"
              type="text"
              style="margin-right: 6px; width: 80px"
            />
            <el-select v-model="formData.DurationUnit" placeholder="单位" style="width: 80px">
              <el-option
                v-for="item in durationUnitData"
                :key="item.Value"
                :label="item.Name"
                :value="item.Value"
              />
            </el-select>
            <span
              style="
                width: 240px;
                display: inline-block;
                color: rgb(204, 204, 204);
                padding: 0 6px;
                font-size: 12px;
              "
            >
              （ 每次坚持时间 ）
            </span>
          </el-form-item>
        </template>
        <el-form-item label="部位">
          <el-select v-model="formData.Part" multiple placeholder="请选择">
            <el-option
              v-for="item in partList"
              :key="item.Id"
              :label="item.Key"
              :value="item.Id!"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="formData.Instrument" placeholder="设备类型" clearable>
            <el-option
              v-for="item in instrumentList"
              :key="item.InstrumentId"
              :label="item.Name"
              :value="item.InstrumentId"
            />
          </el-select>
        </el-form-item>
        <br />
        <el-form-item label="功能障碍">
          <TagsSelect
            v-model="formData.Dysfunction"
            :options="dysfunctionList"
            :props="{ label: 'Key', value: 'Id' }"
            :disabled="isPreview"
          />
        </el-form-item>
        <br />
        <el-form-item label="适用疾病">
          <TagsSelect
            v-model="formData.DiseaseDicts"
            :options="diseaseList"
            :props="{ label: 'Key', value: 'Id' }"
            :disabled="isPreview"
          />
        </el-form-item>
        <br />
        <el-form-item label="封面图">
          <SingleImageUpload v-model="formData.ActionUnitImgURL" />
        </el-form-item>
        <el-form-item label="跟练视频">
          <SingleFileUpload v-model="formData.FollowVideo" :file-type="['video']" />
        </el-form-item>
        <br />
        <el-form-item label="视频图片">
          <div>
            <el-radio-group v-model="formData.MediaType">
              <el-radio :label="0">视频</el-radio>
              <el-radio :label="1">图片</el-radio>
            </el-radio-group>
            <SingleFileUpload
              v-if="formData.MediaType === 0"
              v-model="videoFileList"
              :file-type="['video']"
            />
            <MultiImageUpload v-else v-model="imageFileList" :max-pic-num="6" />
          </div>
        </el-form-item>
        <br />
        <el-form-item label="动作说明" prop="ActionInfo">
          <el-input v-model="formData.ActionInfo" type="textarea" style="width: 700px" />
        </el-form-item>
        <br />
        <el-form-item label="注意事项">
          <el-input v-model="formData.Notes" type="textarea" style="width: 700px" />
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { FormRules } from "element-plus";

const formData = ref<any>({
  Dysfunction: [],
  DiseaseDicts: [],
  ActionUnitImgURL: "",
  FollowVideo: "",
  ActionInfo: "请参考视频训练",
  Notes:
    "量力而行，循序渐进；训练过程中有剧烈疼痛不适，可减轻活动范围或停止训练，并及时联系医生或治疗师。",
});
const activeName = ref<string>("baseInfo");
const isPreview = inject("isPreview") as boolean;
const partList = inject("partList") as ReadDict[];
const instrumentList = inject("instrumentList") as BaseInstrument[];
const dysfunctionList = inject("dysfunctionList") as ReadDict[];
const diseaseList = inject("diseaseList") as ReadDict[];
const typeList = ref<{ Name: string; Value: number }[]>([
  // 类别选择
  {
    Name: "治疗性运动训练",
    Value: 0,
  },
  {
    Name: "日常生活活动能力训练",
    Value: 1,
  },
]);
const durationUnitData = ref<{ Name: string; Value: number }[]>([
  // 时长单位
  {
    Name: "分钟",
    Value: 0,
  },
  {
    Name: "秒",
    Value: 1,
  },
]);
const videoFileList = ref<string>("");
const imageFileList = ref<string[]>([]);

const rules = reactive<FormRules>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
});

const handleGetContentInfo = async (contentId: string) => {
  const res = await Content_Api.queryActionUnitById({ id: contentId });
  if (res.Type === 200) {
    console.log(res.Data);
  }
};

const handleSubmit = async () => {};

interface Props {
  contentId: string;
}
const props = defineProps<Props>();
watch(
  () => props.contentId,
  (newVal) => {
    if (newVal) {
      handleGetContentInfo(newVal);
    }
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>

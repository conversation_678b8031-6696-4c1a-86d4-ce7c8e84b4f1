<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="医院">
                <HospitalSelect
                  v-model="queryParams.orgIdList"
                  :scopeable="true"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                  @change="
                    () => {
                      queryParams.deptIdList = undefined;
                      queryParams.assistantIdList = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.deptIdList"
                  :org-id="queryParams.orgIdList?.[0]"
                  :disabled="queryParams.orgIdList?.length !== 1"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                />
              </el-form-item>
              <el-form-item label="医助">
                <UserSelect
                  v-model="queryParams.assistantIdList"
                  placeholder="请选择医助"
                  :org-ids="queryParams.orgIdList"
                  :dept-ids="queryParams.deptIdList"
                  :scopeable="false"
                  :role-types="['assistant']"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                />
              </el-form-item>
              <el-form-item label="时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  :disabled-date="handleDisabledDate"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length === 0"
              :loading="exportLoading"
              @click="onExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-auto-resizer v-loading="tableLoading" class="table-v2-wrapper">
          <template #default="{ width }">
            <div :style="{ width: `${width}px`, height: `${tableFluidHeight}px` }">
              <el-table-v2
                v-model:sort-state="sortState"
                row-key="DocUserId"
                :header-height="70"
                :estimated-row-height="50"
                :columns="columns"
                :data="data"
                :width="width"
                :height="tableFluidHeight"
                fixed
                @column-sort="onSort"
              >
                <template #header-cell="{ column }">
                  <el-text class="text-#909399! font-600!">{{ column.title }}</el-text>
                </template>
                <template #cell="{ column, rowData }">
                  <el-text class="py-6px!">{{ rowData[column.key] }}</el-text>
                </template>
              </el-table-v2>
            </div>
          </template>
        </el-auto-resizer>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑备注 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    title="备注"
    width="500"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <RemarkForm
      :data="showDataDialog.data"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="tsx">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import {
  convertToRedashParams,
  exportExcel,
  getExportCols,
  getExportTable2Columns,
} from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { ExportEnum } from "@/enums/Other";
import {
  DoctorStatusRedash,
  DoctorStatusStatisticsInputDTO,
  ExportTaskRedashDTO,
} from "@/api/report/types";
import { useUserStore } from "@/store";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import { ElButton, TableV2SortOrder, TableV2FixedDir } from "element-plus";
import type { SortState, SortBy, Column } from "element-plus";

const { datePickerShortcuts } = useDateRangePicker();

interface QueryParams extends RedashParameters<DoctorStatusStatisticsInputDTO> {
  orgIdList?: string[];
  deptIdList?: string[];
  assistantIdList?: string[];
}

// 调试开关
const kEnableDebug = true;
defineOptions({
  name: "DoctorStatusStatistics",
});

// 查询条件
const queryParams = reactive<QueryParams>({
  startTimeDt: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  endTimeDt: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  LoginUserId: useUserStore().userInfo.Id,
  assistantIds: undefined,
  deptIds: undefined,
  orgIds: undefined,
  pageIndex: 1,
  pageSize: 20,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.startTimeDt, queryParams.endTimeDt];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.startTimeDt = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.endTimeDt = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

watch(
  () => queryParams.orgIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.orgIds = undefined;
    } else {
      queryParams.orgIds = newVal?.join(",");
    }
  }
);

watch(
  () => queryParams.deptIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.deptIds = undefined;
    } else {
      queryParams.deptIds = newVal?.join(",");
    }
  }
);

watch(
  () => queryParams.assistantIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.assistantIds = undefined;
    } else {
      queryParams.assistantIds = newVal?.join(",");
    }
  }
);

// 禁用日期
function handleDisabledDate(date: Date) {
  return date.getTime() > Date.now();
}

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

const { pageData, tableLoading, tableFluidHeight, total, tableResize, exportLoading } =
  useTableConfig<DoctorStatusRedash>();

/** 表格列配置 */
const columns: Column<DoctorStatusRedash>[] = [
  {
    key: "Name",
    title: "姓名",
    dataKey: "Name",
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "PhoneNumber",
    title: "手机号",
    dataKey: "PhoneNumber",
    width: 110,
    minWidth: 110,
    align: "center",
  },
  {
    key: "Sex",
    title: "性别",
    dataKey: "Sex",
    width: 60,
    minWidth: 60,
    align: "center",
  },
  {
    key: "Age",
    title: "年龄",
    dataKey: "Age",
    width: 60,
    minWidth: 60,
    align: "center",
  },
  {
    key: "OrgName",
    title: "医院",
    dataKey: "OrgName",
    width: 180,
    minWidth: 180,
    align: "center",
  },
  {
    key: "DeptName",
    title: "科室",
    dataKey: "DeptName",
    width: 180,
    minWidth: 180,
    align: "center",
  },
  {
    key: "AssistantName",
    title: "医助",
    dataKey: "AssistantName",
    width: 110,
    minWidth: 110,
    align: "center",
  },
  {
    key: "PracticeLevel",
    title: "职称",
    dataKey: "PracticeLevel",
    width: 90,
    minWidth: 90,
    align: "center",
  },
  {
    key: "RoleName",
    title: "角色",
    dataKey: "RoleName",
    width: 70,
    minWidth: 70,
    align: "center",
  },
  {
    key: "newPatientCount",
    title: "新增患者数",
    dataKey: "newPatientCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "totalPatientCount",
    title: "累计患者数",
    dataKey: "totalPatientCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newConsultPatientCount",
    title: "新增医疗患者数",
    dataKey: "newConsultPatientCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newReConsultPatientCount",
    title: "新增复诊患者数",
    dataKey: "newReConsultPatientCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newPrescriptionCount",
    title: "新增方案数",
    dataKey: "newPrescriptionCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newExecuteCount",
    title: "新增执行方案数",
    dataKey: "newExecuteCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newPayCount",
    title: "新增付费方案数",
    dataKey: "newPayCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newCiReCount",
    title: "新增执行磁疗方案数",
    dataKey: "newCiReCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newCiReUnitCount",
    title: "新增执行磁疗人天数",
    dataKey: "newCiReUnitCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newGeWuCount",
    title: "新增执行隔物灸方案数",
    dataKey: "newGeWuCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newGeWuUnitCount",
    title: "新增隔物灸贴数",
    dataKey: "newGeWuUnitCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newMaiZhenCount",
    title: "新增执行埋针方案数",
    dataKey: "newMaiZhenCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newMaiZhenUnitCount",
    title: "新增埋针次数",
    dataKey: "newMaiZhenUnitCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newConsultCount",
    title: "新增问诊/咨询数",
    dataKey: "newConsultCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "newPayConsultCount",
    title: "新增付费问诊/咨询数",
    dataKey: "newPayConsultCount",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "profileDescription",
    title: "资料是否完整",
    dataKey: "profileDescription",
    width: 120,
    minWidth: 120,
    align: "center",
  },
  {
    key: "enableConsult",
    title: "是否开启服务",
    dataKey: "enableConsult",
    sortable: true,
    width: 60,
    minWidth: 60,
    align: "center",
  },
  {
    key: "showConsultCost",
    title: "服务价格",
    dataKey: "showConsultCost",
    sortable: true,
    width: 70,
    minWidth: 70,
    align: "center",
  },
  {
    key: "isLoginIn7Day",
    title: "本周是否登录",
    dataKey: "isLoginIn7Day",
    sortable: true,
    width: 60,
    minWidth: 60,
    align: "center",
  },
  {
    key: "isTest",
    title: "是否测试数据",
    dataKey: "isTest",
    sortable: true,
    width: 80,
    minWidth: 80,
    align: "center",
  },
  {
    key: "operations",
    title: "操作",
    width: 100,
    align: "center",
    fixed: TableV2FixedDir.RIGHT,
    cellRenderer: ({ rowData }) => (
      <ElButton link type="primary" onClick={() => onRemark(rowData)}>
        备注
      </ElButton>
    ),
  },
];

/** 表格数据 */
const data = computed(() =>
  pageData.value.map((row) => {
    return {
      id: row.DocUserId,
      ...row,
    };
  })
);

/** 需要排序的列 */
const sortState = ref<SortState>({
  newPatientCount: TableV2SortOrder.DESC,
  totalPatientCount: TableV2SortOrder.DESC,
  newConsultPatientCount: TableV2SortOrder.DESC,
  newReConsultPatientCount: TableV2SortOrder.DESC,
  newPrescriptionCount: TableV2SortOrder.DESC,
  newExecuteCount: TableV2SortOrder.DESC,
  newPayCount: TableV2SortOrder.DESC,
  newCiReCount: TableV2SortOrder.DESC,
  newCiReUnitCount: TableV2SortOrder.DESC,
  newGeWuCount: TableV2SortOrder.DESC,
  newGeWuUnitCount: TableV2SortOrder.DESC,
  newMaiZhenCount: TableV2SortOrder.DESC,
  newMaiZhenUnitCount: TableV2SortOrder.DESC,
  newConsultCount: TableV2SortOrder.DESC,
  newPayConsultCount: TableV2SortOrder.DESC,
  enableConsult: TableV2SortOrder.DESC,
  showConsultCost: TableV2SortOrder.DESC,
  isLoginIn7Day: TableV2SortOrder.DESC,
  isTest: TableV2SortOrder.DESC,
});

/**
 * 对表格中某列数据排序
 *
 * @param key 排序字段
 * @param order 排序顺序
 */
function sortData(key: keyof DoctorStatusRedash, order: TableV2SortOrder) {
  const compareValue = (value: any) => {
    if (typeof value === "string" && value === "是") {
      return 1;
    } else if (typeof value === "string" && value === "否") {
      return 0;
    } else if (typeof value === "string") {
      return Number(value) ?? 0;
    } else {
      return value;
    }
  };

  pageData.value = pageData.value.sort((a, b) => {
    return order === TableV2SortOrder.ASC
      ? compareValue(a[key]) - compareValue(b[key])
      : compareValue(b[key]) - compareValue(a[key]);
  });
}

/**
 * 排序事件
 *
 * @param key 排序字段
 * @param order 排序顺序
 */
function onSort({ key, order }: SortBy) {
  kEnableDebug && console.debug("排序", key, order);
  sortState.value[key] = order;
  sortData(key as keyof DoctorStatusRedash, order);
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const { assistantIdList, deptIdList, orgIdList, ...parameters } = queryParams;

  const params = convertToRedashParams(parameters, "Report_DoctorStatistics");
  const r = await Report_Api.getRedashList<DoctorStatusRedash>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  queryResultId = r.Data.QueryResultId;
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

/** 查询结果Id */
let queryResultId = -1;

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const { assistantIdList, deptIdList, orgIdList, ...parameters } = queryParams;
  const exportParams = convertToRedashParams(parameters, "Report_DoctorStatistics");
  const params: ExportTaskRedashDTO = {
    Cols: getExportTable2Columns(columns, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `医生情况统计-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
    QueryName: "Report_DoctorStatistics",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 添加/编辑备注弹窗
const showDataDialog = reactive({
  isShow: false,
  data: {} as DoctorStatusRedash,
});

// 点击备注
function onRemark(row: DoctorStatusRedash) {
  kEnableDebug && console.debug("点击备注", row);

  if (!row.DocUserId) {
    ElMessage.error("医生Id为空");
    return;
  }

  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

// 确认提交备注
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确认提交备注");

  showDataDialog.isShow = false;
  ElNotification.success("备注成功");

  queryParams.pageIndex = 1;
  requestTableList();
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped>
.table-v2-wrapper {
  border: 1px solid #ebeef5;
}

:deep(.el-table-v2__header-cell) {
  border-left: 1px solid #ebeef5;
}

:deep(.el-table-v2__row-cell) {
  border-left: 1px solid #ebeef5;
}
</style>
